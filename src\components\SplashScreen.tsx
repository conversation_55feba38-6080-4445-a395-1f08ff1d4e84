
import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface SplashScreenProps {
  onComplete: () => void;
}

const SplashScreen = ({ onComplete }: SplashScreenProps) => {
  const [isAnimating, setIsAnimating] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAnimating(false);
      setTimeout(onComplete, 500); // Allow exit animation to complete
    }, 2500);

    return () => clearTimeout(timer);
  }, [onComplete]);

  return (
    <motion.div
      className="fixed inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-amber-light to-blue-light"
      initial={{ opacity: 1 }}
      animate={{ opacity: isAnimating ? 1 : 0 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.8, type: "spring" }}
        className="mb-8"
      >
        <div className="relative">
          <motion.div
            className="w-32 h-32 bg-amber rounded-full"
            animate={{ 
              y: [0, -10, 0],
            }}
            transition={{ repeat: Infinity, duration: 2.5 }}
          />
          <motion.div 
            className="absolute -right-5 -top-5 w-20 h-20 bg-pink rounded-full"
            animate={{ 
              y: [0, -15, 0],
            }}
            transition={{ repeat: Infinity, duration: 3, delay: 0.5 }}
          />
          <motion.div 
            className="absolute -left-8 top-10 w-16 h-16 bg-blue rounded-full"
            animate={{ 
              y: [0, -12, 0],
            }}
            transition={{ repeat: Infinity, duration: 3.5, delay: 0.7 }}
          />
          <motion.div 
            className="absolute -bottom-2 right-10 w-14 h-14 bg-purple rounded-full"
            animate={{ 
              y: [0, -8, 0],
            }}
            transition={{ repeat: Infinity, duration: 2, delay: 0.3 }}
          />
        </div>
      </motion.div>
      
      <motion.h1 
        className="text-4xl md:text-5xl font-bold text-center text-slate-800"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.6, duration: 0.8 }}
      >
        Stories for All
      </motion.h1>
      
      <motion.p
        className="text-lg mt-4 text-center text-slate-600 max-w-md px-6"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.9, duration: 0.8 }}
      >
        Where every parent and teacher becomes a storyteller
      </motion.p>
    </motion.div>
  );
};

export default SplashScreen;
