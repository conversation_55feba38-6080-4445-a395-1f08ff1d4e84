
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { motion } from 'framer-motion';
import { ArrowLeft, ArrowRight, Palette } from 'lucide-react';
import type { StoryData } from '@/hooks/useStoryGeneration';

interface StyleOption {
  id: string;
  name: string;
  description: string;
  previewUrl: string;
}

interface IllustrationStyleStepProps {
  storyData: StoryData;
  onBack: () => void;
  onNext: (selectedStyle: string) => void;
}

const styleOptions: StyleOption[] = [
  {
    id: 'watercolor',
    name: 'Watercolor Dreams',
    description: 'Soft, flowing watercolor illustrations with gentle textures',
    previewUrl: '/placeholder.svg'
  },
  {
    id: 'cartoon',
    name: 'Playful Cartoon',
    description: 'Bright, bold cartoon characters with expressive features',
    previewUrl: '/placeholder.svg'
  },
  {
    id: 'papercut',
    name: 'Paper Cutouts',
    description: 'Charming illustrations that look like layered paper cutouts',
    previewUrl: '/placeholder.svg'
  },
  {
    id: 'pencil',
    name: 'Pencil Sketch',
    description: 'Hand-drawn pencil illustrations with a classic storybook feel',
    previewUrl: '/placeholder.svg'
  },
  {
    id: '3d',
    name: '3D Wonderland',
    description: 'Modern 3D characters and scenes with depth and dimension',
    previewUrl: '/placeholder.svg'
  },
  {
    id: 'collage',
    name: 'Mixed Media',
    description: 'Creative collage style combining photos, drawings, and textures',
    previewUrl: '/placeholder.svg'
  },
  {
    id: 'custom',
    name: 'Custom Style',
    description: 'Describe your own unique illustration style',
    previewUrl: '/placeholder.svg'
  }
];

const IllustrationStyleStep = ({ storyData, onBack, onNext }: IllustrationStyleStepProps) => {
  const [selectedStyle, setSelectedStyle] = useState<string>('');
  const [customStyleDescription, setCustomStyleDescription] = useState<string>('');

  const handleStyleSelect = (styleId: string) => {
    setSelectedStyle(styleId);
  };

  const handleContinue = () => {
    if (selectedStyle) {
      // If custom style is selected, use the custom description, otherwise use the style ID
      const styleToPass = selectedStyle === 'custom' ? customStyleDescription : selectedStyle;
      onNext(styleToPass);
    }
  };

  const isCustomStyleValid = selectedStyle !== 'custom' || (selectedStyle === 'custom' && customStyleDescription.trim().length > 0);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-4xl mx-auto"
    >
      <Card className="border-2 border-purple/30 shadow-story rounded-xl overflow-hidden">
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold text-slate-800 mb-6 text-center">
            <span className="emoji-hint">🎨</span> Choose Your Illustration Style
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
            {styleOptions.map((style, index) => (
              <motion.div
                key={style.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`style-chip ${selectedStyle === style.id ? 'active' : ''}`}
                onClick={() => handleStyleSelect(style.id)}
              >
                <div className="aspect-[4/3] bg-slate-100 rounded-lg mb-3 overflow-hidden flex items-center justify-center">
                  {style.id === 'custom' ? (
                    <Palette className="h-12 w-12 text-slate-400" />
                  ) : (
                    <img
                      src={style.previewUrl}
                      alt={style.name}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
                <h3 className="font-semibold">{style.name}</h3>
                <p className="text-sm text-slate-600">{style.description}</p>
              </motion.div>
            ))}
          </div>

          {selectedStyle === 'custom' && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-6"
            >
              <Card className="border-2 border-primary/30 bg-primary/5">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2 mb-4">
                      <Palette className="h-5 w-5 text-primary" />
                      <h3 className="text-lg font-semibold text-slate-800">Describe Your Custom Style</h3>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="customStyle" className="text-sm font-medium">
                        Style Description
                      </Label>
                      <Textarea
                        id="customStyle"
                        placeholder="Describe your desired illustration style... (e.g., 'vintage storybook illustrations with warm earth tones and hand-drawn textures', 'modern minimalist line art with pastel colors', 'realistic oil painting style with dramatic lighting')"
                        value={customStyleDescription}
                        onChange={(e) => setCustomStyleDescription(e.target.value)}
                        className="min-h-[100px] resize-none"
                        maxLength={500}
                      />
                      <p className="text-xs text-slate-500">
                        {customStyleDescription.length}/500 characters
                      </p>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-sm text-blue-800">
                        <strong>💡 Tip:</strong> Be specific about colors, textures, artistic techniques, and mood.
                        The more detailed your description, the better the AI can match your vision!
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          <div className="flex justify-between pt-4">
            <Button
              onClick={onBack}
              variant="outline"
              className="h-12 px-6"
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            <Button
              onClick={handleContinue}
              disabled={!selectedStyle || !isCustomStyleValid}
              className="bg-purple hover:bg-purple-dark text-white h-12 px-6"
            >
              Preview Story <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default IllustrationStyleStep;
