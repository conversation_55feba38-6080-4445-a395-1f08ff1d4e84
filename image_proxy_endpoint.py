# Add this endpoint to your existing FastAPI server at localhost:5679

from fastapi import HTTPException
from pydantic import BaseModel
import requests
import io
from fastapi.responses import StreamingResponse

class ImageProxyRequest(BaseModel):
    image_url: str

@app.post("/proxy-image")
async def proxy_image(request: ImageProxyRequest):
    """
    Proxy endpoint to fetch images from Google Cloud Storage
    and return them without CORS restrictions
    """
    try:
        print(f"Proxying image: {request.image_url}")
        
        # Fetch the image from Google Cloud Storage
        response = requests.get(request.image_url, timeout=30)
        
        if response.status_code != 200:
            raise HTTPException(
                status_code=response.status_code, 
                detail=f"Failed to fetch image: {response.status_code}"
            )
        
        # Get the content type from the original response
        content_type = response.headers.get('content-type', 'image/jpeg')
        
        # Create a streaming response with the image data
        return StreamingResponse(
            io.BytesIO(response.content),
            media_type=content_type,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "*",
            }
        )
        
    except requests.RequestException as e:
        print(f"Error fetching image {request.image_url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch image: {str(e)}")
    except Exception as e:
        print(f"Unexpected error proxying image {request.image_url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

# Also add CORS middleware if not already present
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
