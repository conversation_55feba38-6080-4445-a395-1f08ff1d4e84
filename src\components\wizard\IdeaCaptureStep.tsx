import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { Plus, X } from 'lucide-react';

interface Character {
  name: string;
  description: string;
}

interface IdeaCaptureStepProps {
  formData: {
    title: string;
    characters: Character[];
    setting: string;
    plotIdea: string;
    ageGroup: string;
    numPages: number;
    language: string;
  };
  setFormData: React.Dispatch<
    React.SetStateAction<{
      title: string;
      characters: Character[];
      setting: string;
      plotIdea: string;
      ageGroup: string;
      numPages: number;
      language: string;
    }>
  >;
  onNext: () => void;
}

const IdeaCaptureStep = ({ formData, setFormData, onNext }: IdeaCaptureStepProps) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === 'numPages') {
      let num = Math.max(1, Math.min(12, Number(value)));
      setFormData((prev) => ({ ...prev, numPages: num }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const addCharacter = () => {
    setFormData((prev) => ({
      ...prev,
      characters: [...prev.characters, { name: '', description: '' }]
    }));
  };

  const removeCharacter = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      characters: prev.characters.filter((_, i) => i !== index)
    }));
  };

  const updateCharacter = (index: number, field: keyof Character, value: string) => {
    setFormData((prev) => ({
      ...prev,
      characters: prev.characters.map((char, i) => 
        i === index ? { ...char, [field]: value } : char
      )
    }));
  };

  const isFormValid = () => {
    return formData.title && 
           formData.characters.length > 0 && 
           formData.characters.every(char => char.name.trim()) && 
           formData.plotIdea &&
           formData.numPages >= 1 && formData.numPages <= 12 &&
           formData.language;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-3xl mx-auto"
    >
      <Card className="border-2 border-amber/30 shadow-story rounded-xl overflow-hidden">
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold text-slate-800 mb-6 text-center">
            <span className="emoji-hint">✨</span> Let's Start Your Story
          </h2>
          
          <div className="space-y-6">
            <div>
              <Label htmlFor="title" className="text-lg mb-1">
                Story Title
              </Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                placeholder="The Magical Forest Adventure"
                className="text-base p-4 h-auto tap-target"
              />
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-3">
                <Label className="text-lg">Characters</Label>
                <Button
                  type="button"
                  onClick={addCharacter}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Character
                </Button>
              </div>
              
              {formData.characters.length === 0 && (
                <div className="text-center py-4 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
                  <p>No characters added yet. Click "Add Character" to start!</p>
                </div>
              )}
              
              <div className="space-y-4">
                {formData.characters.map((character, index) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <Label className="text-base font-medium">
                        Character {index + 1}
                      </Label>
                      {formData.characters.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeCharacter(index)}
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-700"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor={`character-name-${index}`} className="text-sm">
                          Name
                        </Label>
                        <Input
                          id={`character-name-${index}`}
                          value={character.name}
                          onChange={(e) => updateCharacter(index, 'name', e.target.value)}
                          placeholder="Lily the brave rabbit"
                          className="text-base p-3 h-auto"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor={`character-desc-${index}`} className="text-sm">
                          Description
                        </Label>
                        <Textarea
                          id={`character-desc-${index}`}
                          value={character.description}
                          onChange={(e) => updateCharacter(index, 'description', e.target.value)}
                          placeholder="A small, curious rabbit with bright blue eyes and a red scarf..."
                          className="min-h-[80px] text-base p-3"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {formData.characters.length === 0 && (
                <Button
                  type="button"
                  onClick={addCharacter}
                  variant="outline"
                  className="w-full mt-2"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Character
                </Button>
              )}
            </div>
            
            <div>
              <Label htmlFor="setting" className="text-lg">
                Brief Story Description
              </Label>
              <Textarea
                id="setting"
                name="setting"
                value={formData.setting}
                onChange={handleChange}
                placeholder="A heartwarming tale about friendship and adventure in a magical world..."
                className="min-h-[80px] text-base p-4 tap-target"
              />
            </div>
            
            <div>
              <Label htmlFor="plotIdea" className="text-lg">
                <span className="emoji-hint">📝</span> Plot Idea
              </Label>
              <Textarea
                id="plotIdea"
                name="plotIdea"
                value={formData.plotIdea}
                onChange={handleChange}
                placeholder="Lily discovers a magical key that opens doors to different worlds..."
                className="min-h-[100px] text-base p-4 tap-target"
              />
            </div>
            
            <div>
              <Label htmlFor="ageGroup" className="text-lg">
                Target Age Group
              </Label>
              <select
                id="ageGroup"
                name="ageGroup"
                value={formData.ageGroup}
                onChange={handleChange as any}
                className="w-full rounded-md border border-input bg-background px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">Select age group...</option>
                <option value="2-4">Toddlers (2-4 years)</option>
                <option value="5-7">Early readers (5-7 years)</option>
                <option value="8-10">Independent readers (8-10 years)</option>
              </select>
            </div>
            
            <div>
              <Label htmlFor="language" className="text-lg">
                Language
              </Label>
              <select
                id="language"
                name="language"
                value={formData.language}
                onChange={handleChange as any}
                className="w-full rounded-md border border-input bg-background px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">Select language...</option>
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="nl">Dutch</option>
                <option value="de">German</option>
              </select>
            </div>
            
            <div>
              <Label htmlFor="numPages" className="text-lg">
                Number of pages/scenes
              </Label>
              <Input
                id="numPages"
                name="numPages"
                type="number"
                min={1}
                max={12}
                value={formData.numPages}
                onChange={handleChange}
                className="text-base p-4 h-auto tap-target"
              />
              <p className="text-xs text-gray-500 mt-1">Choose between 1 and 12 pages (default is 8).</p>
            </div>
            
            <div className="pt-4">
              <Button
                onClick={onNext}
                disabled={!isFormValid()}
                className="w-full bg-amber hover:bg-amber-dark text-primary-foreground h-12 text-lg"
              >
                Continue to Draft
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default IdeaCaptureStep;
