-- Create story_images table to store image metadata
CREATE TABLE IF NOT EXISTS public.story_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    story_id UUID NOT NULL REFERENCES public.user_stories(id) ON DELETE CASCADE,
    scene_id TEXT NOT NULL,
    image_url TEXT NOT NULL,
    page_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_story_images_story_id ON public.story_images(story_id);
CREATE INDEX IF NOT EXISTS idx_story_images_scene_id ON public.story_images(scene_id);
CREATE INDEX IF NOT EXISTS idx_story_images_page_number ON public.story_images(page_number);
CREATE INDEX IF NOT EXISTS idx_story_images_created_at ON public.story_images(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE public.story_images ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (for now, allow all operations - you can restrict later)
CREATE POLICY "Allow all operations on story_images" ON public.story_images
    FOR ALL USING (true);

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER handle_story_images_updated_at
    BEFORE UPDATE ON public.story_images
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();
