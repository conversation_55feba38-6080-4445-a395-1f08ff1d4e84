#!/usr/bin/env python3
"""
Test script to verify the image proxy endpoint works
"""

import requests
import json

def test_image_proxy():
    # Test URL - replace with one of your actual image URLs
    test_image_url = "https://storage.googleapis.com/kinderboek/test_scene_1.png"
    
    proxy_url = "http://localhost:5679/proxy-image"
    
    payload = {
        "image_url": test_image_url
    }
    
    try:
        print(f"Testing proxy with image: {test_image_url}")
        
        response = requests.post(
            proxy_url,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            print(f"✅ Proxy works! Response size: {len(response.content)} bytes")
            print(f"Content-Type: {response.headers.get('content-type')}")
            
            # Save the image to verify it's valid
            with open("test_proxied_image.jpg", "wb") as f:
                f.write(response.content)
            print("✅ Image saved as test_proxied_image.jpg")
            
        else:
            print(f"❌ Proxy failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_image_proxy()
