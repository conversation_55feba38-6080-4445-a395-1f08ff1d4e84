import { useState } from 'react';
import jsPDF from 'jspdf';

// Convert Google Cloud Storage gs:// URLs to HTTPS URLs
const convertGcsUriToHttps = (gcsUri: string): string => {
  if (gcsUri.startsWith('gs://')) {
    // Convert gs://bucket-name/path to https://storage.googleapis.com/bucket-name/path
    return gcsUri.replace('gs://', 'https://storage.googleapis.com/');
  }
  return gcsUri;
};

interface StoryPage {
  text: string;
  sceneDescription?: string;
  imageUrl?: string;
}

interface StoryData {
  title: string;
  pages: StoryPage[];
  style?: string;
  mainCharacter?: string;
  ageGroup?: string;
}

export const usePDFGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);

  const loadImageViaLocalProxy = async (src: string): Promise<string> => {
    console.log('Loading image via local API proxy:', src);

    try {
      // Use your existing API server as a proxy
      const response = await fetch('http://localhost:5679/proxy-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image_url: src
        })
      });

      if (!response.ok) {
        throw new Error(`Local proxy failed: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      console.log('Image fetched as blob via local proxy, size:', blob.size);

      // Convert blob to data URL
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const dataUrl = reader.result as string;
          console.log('Image converted to data URL via local proxy successfully');
          resolve(dataUrl);
        };
        reader.onerror = () => {
          console.error('Failed to convert blob to data URL');
          reject(new Error('Failed to convert blob to data URL'));
        };
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Failed to load image via local proxy:', src, error);
      throw error;
    }
  };

  const loadImageAsDataUrl = async (src: string): Promise<string> => {
    console.log('Converting image to data URL:', src);

    try {
      // Try the local API proxy first
      return await loadImageViaLocalProxy(src);
    } catch (proxyError) {
      console.warn('Local proxy failed, this is expected if proxy endpoint not implemented:', proxyError);

      // For now, just throw an error with a helpful message
      throw new Error(`Image loading failed. The image at ${src} cannot be loaded due to CORS restrictions. A server-side proxy endpoint is needed.`);
    }
  };

  const generatePDF = async (storyData: StoryData): Promise<void> => {
    setIsGenerating(true);

    try {
      console.log('Starting PDF generation for story:', storyData.title);
      console.log('Story pages:', storyData.pages);

      // Create PDF with A4 landscape dimensions
      const pdf = new jsPDF('l', 'mm', 'a4'); // 'l' for landscape
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const contentWidth = pageWidth - (margin * 2);

      console.log(`PDF dimensions: ${pageWidth}x${pageHeight}mm (landscape)`);
      console.log(`Content area: ${contentWidth}mm wide`);

      // Add title page
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      const titleLines = pdf.splitTextToSize(storyData.title, contentWidth);
      const titleHeight = titleLines.length * 10;
      const titleY = (pageHeight - titleHeight) / 2;

      pdf.text(titleLines, pageWidth / 2, titleY, { align: 'center' });

      // Add story metadata
      if (storyData.mainCharacter || storyData.ageGroup || storyData.style) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        let metaY = titleY + titleHeight + 20;

        if (storyData.mainCharacter) {
          pdf.text(`Main Character: ${storyData.mainCharacter}`, pageWidth / 2, metaY, { align: 'center' });
          metaY += 8;
        }

        if (storyData.ageGroup) {
          pdf.text(`Age Group: ${storyData.ageGroup}`, pageWidth / 2, metaY, { align: 'center' });
          metaY += 8;
        }

        if (storyData.style) {
          pdf.text(`Illustration Style: ${storyData.style}`, pageWidth / 2, metaY, { align: 'center' });
        }
      }

      // Add story pages
      for (let i = 0; i < storyData.pages.length; i++) {
        const page = storyData.pages[i];
        console.log(`Processing page ${i + 1} of ${storyData.pages.length}`);

        // Add new page for each story page
        pdf.addPage();

        let currentY = margin;

        // Add page number
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        pdf.text(`Page ${i + 1}`, pageWidth - margin, margin, { align: 'right' });
        currentY += 15;

        // Add image if available (at the top of the page)
        if (page.imageUrl && page.imageUrl !== '/placeholder.svg') {
          try {
            console.log(`Loading image for page ${i + 1}:`, page.imageUrl);
            // Convert GCS URLs to HTTPS if needed
            const imageUrl = convertGcsUriToHttps(page.imageUrl);
            console.log(`Loading image for page ${i + 1}:`, page.imageUrl, '-> converted to:', imageUrl);

            console.log(`Starting image load for page ${i + 1}...`);
            const dataUrl = await loadImageAsDataUrl(imageUrl);
            console.log(`Image loaded successfully for page ${i + 1}, data URL length:`, dataUrl.length);
            console.log(`Data URL prefix:`, dataUrl.substring(0, 50));

            // Use default dimensions and let jsPDF handle the sizing
            const maxImageHeight = (pageHeight - margin * 2) * 0.65; // 65% of page height
            const maxImageWidth = contentWidth * 0.8; // 80% of page width

            // Use a reasonable default aspect ratio if we can't determine it
            let imageWidth = maxImageWidth;
            let imageHeight = maxImageWidth * 0.75; // 4:3 aspect ratio default

            // If height is too large, scale by height instead
            if (imageHeight > maxImageHeight) {
              imageHeight = maxImageHeight;
              imageWidth = imageHeight * (4/3); // Maintain 4:3 ratio
            }

            // Center the image horizontally
            const imageX = (pageWidth - imageWidth) / 2;

            console.log(`Adding image to PDF for page ${i + 1} at position (${imageX}, ${currentY}) with size ${imageWidth}x${imageHeight}mm`);

            try {
              // Add the image using data URL (always JPEG format)
              pdf.addImage(dataUrl, 'JPEG', imageX, currentY, imageWidth, imageHeight);
              currentY += imageHeight + 20; // More space between image and text
              console.log(`Image added successfully for page ${i + 1} (JPEG, ${imageWidth}x${imageHeight}mm)`);
            } catch (addImageError) {
              console.error(`Failed to add image to PDF for page ${i + 1}:`, addImageError);
              // Add placeholder text instead
              pdf.setFontSize(12);
              pdf.setFont('helvetica', 'italic');
              pdf.text('[Image could not be added to PDF]', pageWidth / 2, currentY, { align: 'center' });
              currentY += 30;
              pdf.setFontSize(16);
              pdf.setFont('helvetica', 'normal');
            }
          } catch (error) {
            console.error(`Failed to load image for page ${i + 1}:`, error);
            // Add a placeholder text when image fails
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'italic');
            pdf.text('[Image not available]', pageWidth / 2, currentY, { align: 'center' });
            currentY += 30;
            pdf.setFontSize(14);
            pdf.setFont('helvetica', 'normal');
          }
        } else {
          console.log(`No image URL for page ${i + 1} or placeholder image`);
        }

        // Add text content below the image
        console.log(`Adding text content for page ${i + 1}:`, page.text.substring(0, 50) + '...');
        pdf.setFontSize(16); // Slightly larger text for landscape
        pdf.setFont('helvetica', 'normal');

        // Use more of the width for text in landscape mode
        const textWidth = contentWidth * 0.9;
        const textX = (pageWidth - textWidth) / 2; // Center the text

        const textLines = pdf.splitTextToSize(page.text, textWidth);
        const remainingHeight = pageHeight - currentY - margin;
        const lineHeight = 10; // Slightly more line height for readability
        const maxLines = Math.floor(remainingHeight / lineHeight);

        console.log(`Text for page ${i + 1}: ${textLines.length} lines, max lines: ${maxLines}, remaining height: ${remainingHeight}mm`);

        if (textLines.length <= maxLines) {
          pdf.text(textLines, textX, currentY);
          console.log(`Text added successfully for page ${i + 1}`);
        } else {
          // Split text across multiple pages if needed
          console.log(`Text too long for page ${i + 1}, splitting across multiple pages`);
          let lineIndex = 0;
          while (lineIndex < textLines.length) {
            const pageLinesCount = Math.min(maxLines, textLines.length - lineIndex);
            const pageLines = textLines.slice(lineIndex, lineIndex + pageLinesCount);

            pdf.text(pageLines, textX, currentY);
            lineIndex += pageLinesCount;

            if (lineIndex < textLines.length) {
              pdf.addPage();
              currentY = margin + 15; // Reset Y position for new page

              // Add page number for continuation page
              pdf.setFontSize(10);
              pdf.text(`Page ${i + 1} (continued)`, pageWidth - margin, margin, { align: 'right' });
              pdf.setFontSize(16);
            }
          }
          console.log(`Text splitting completed for page ${i + 1}`);
        }

        console.log(`Completed processing page ${i + 1}`);
      }

      console.log('All pages processed, saving PDF...');

      // Save the PDF
      const fileName = `${storyData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_story.pdf`;
      pdf.save(fileName);

    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error('Failed to generate PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    generatePDF,
    isGenerating
  };
};
