import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { ArrowLeft, ArrowRight, RefreshCw } from 'lucide-react';
import { useStoryGeneration } from '@/hooks/useStoryGeneration';
import { useToast } from '@/hooks/use-toast';

interface Character {
  name: string;
  description: string;
}

interface StoryDraftStepProps {
  formData: {
    title: string;
    characters: Character[];
    setting: string;
    plotIdea: string;
    ageGroup: string;
    numPages: number;
    language: string;
  };
  onBack: () => void;
  onNext: (storyData: { storyText: string; pages: any[]; storyData: any }) => void;
}

const StoryDraftStep = ({ formData, onBack, onNext }: StoryDraftStepProps) => {
  const [storyData, setStoryData] = useState<{ storyText: string; pages: any[]; storyData: any } | null>(null);
  const { generateStory, isGenerating, error } = useStoryGeneration();
  const { toast } = useToast();

  const handleGenerateStory = async () => {
    try {
      const result = await generateStory(formData);
      setStoryData(result);
    } catch (err) {
      toast({
        title: "Story Generation Failed",
        description: "Please try again. Check your internet connection and try once more.",
        variant: "destructive",
      });
    }
  };

  const handleContinue = () => {
    if (storyData) {
      onNext(storyData);
    }
  };

  // Auto-generate story when component mounts
  useEffect(() => {
    handleGenerateStory();
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-3xl mx-auto"
    >
      <Card className="border-2 border-blue/30 shadow-story rounded-xl overflow-hidden">
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold text-slate-800 mb-6 text-center">
            <span className="emoji-hint">📚</span> Your Story Draft
          </h2>

          {isGenerating ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="relative">
                <div className="w-16 h-16 border-4 border-muted rounded-full"></div>
                <div className="w-16 h-16 border-t-4 border-blue animate-rotate-slow absolute top-0 rounded-full"></div>
              </div>
              <p className="mt-6 text-muted-foreground">Creating your personalized story...</p>
              <p className="mt-2 text-sm text-muted-foreground">This may take a few moments</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="text-red-500 mb-4">
                <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <p className="text-center text-muted-foreground mb-4">Oops! Something went wrong generating your story.</p>
              <Button onClick={handleGenerateStory} className="bg-blue hover:bg-blue-dark">
                <RefreshCw className="mr-2 h-4 w-4" /> Try Again
              </Button>
            </div>
          ) : storyData ? (
            <>
              <div className="bg-slate-50 p-6 rounded-xl border border-slate-200 mb-8">
                <h3 className="text-xl font-bold mb-4">{formData.title}</h3>
                <div className="prose prose-sm max-w-none">
                  {storyData.storyText.split('\n\n').map((paragraph, i) => (
                    <p key={i} className="mb-4">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>

              <div className="flex justify-center mb-6">
                <Button
                  onClick={handleGenerateStory}
                  variant="outline"
                  className="flex items-center"
                >
                  <RefreshCw className="mr-2 h-4 w-4" /> Generate Different Story
                </Button>
              </div>

              <div className="flex justify-between pt-4">
                <Button
                  onClick={onBack}
                  variant="outline"
                  className="h-12 px-6"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Back
                </Button>
                <Button
                  onClick={handleContinue}
                  className="bg-blue hover:bg-blue-dark text-primary-foreground h-12 px-6"
                >
                  Choose Illustration Style <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </>
          ) : null}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default StoryDraftStep;
