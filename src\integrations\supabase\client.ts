// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dzhzqxgfqwmhtugdhfkk.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR6aHpxeGdmcXdtaHR1Z2RoZmtrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI0MTgxMTcsImV4cCI6MjA1Nzk5NDExN30.sSoFtpxJOe-Y0SFLfqVdcDUCESrVssBXGwWi821Egr8";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);