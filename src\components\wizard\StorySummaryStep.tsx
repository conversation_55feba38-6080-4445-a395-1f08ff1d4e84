import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { BookOpen, Home } from 'lucide-react';

interface StorySummaryStepProps {
  formData: {
    title: string;
    mainCharacter: string;
    setting: string;
    plotIdea: string;
    ageGroup: string;
    numPages: number;
    language: string;
  };
  selectedStyle: string;
  onStartNew: () => void;
  onViewLibrary: () => void;
}

const StorySummaryStep = ({ 
  formData, 
  selectedStyle,
  onStartNew,
  onViewLibrary
}: StorySummaryStepProps) => {
  const styleNames: Record<string, string> = {
    'watercolor': 'Watercolor Dreams',
    'cartoon': 'Playful Cartoon',
    'papercut': 'Paper Cutouts',
    'pencil': 'Pencil Sketch',
    '3d': '3D Wonderland',
    'collage': 'Mixed Media'
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-3xl mx-auto"
    >
      <Card className="border-2 border-amber/30 shadow-story rounded-xl overflow-hidden">
        <CardContent className="p-6">
          <div className="flex justify-center mb-8">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          
          <h2 className="text-2xl font-bold text-center text-slate-800 mb-2">
            Story Created Successfully!
          </h2>
          <p className="text-center text-slate-600 mb-8">
            Your story has been saved to your library
          </p>
          
          <div className="bg-slate-50 p-6 rounded-xl border border-slate-200 mb-8">
            <h3 className="font-bold text-xl mb-4">{formData.title}</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-slate-500">Main Character</p>
                <p className="font-medium">{formData.mainCharacter}</p>
              </div>
              <div>
                <p className="text-sm text-slate-500">Setting</p>
                <p className="font-medium">{formData.setting}</p>
              </div>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-slate-500">Illustration Style</p>
              <p className="font-medium">{styleNames[selectedStyle] || selectedStyle}</p>
            </div>
            
            <div>
              <p className="text-sm text-slate-500">Target Age Group</p>
              <p className="font-medium">
                {formData.ageGroup === '2-4' ? 'Toddlers (2-4 years)' : 
                 formData.ageGroup === '5-7' ? 'Early readers (5-7 years)' :
                 formData.ageGroup === '8-10' ? 'Independent readers (8-10 years)' :
                 'All ages'}
              </p>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-slate-500">Number of pages/scenes</p>
              <p className="font-medium">{formData.numPages}</p>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-slate-500">Language</p>
              <p className="font-medium">
                {formData.language === 'en' ? 'English' :
                 formData.language === 'es' ? 'Spanish' :
                 formData.language === 'fr' ? 'French' :
                 formData.language === 'nl' ? 'Dutch' :
                 formData.language === 'de' ? 'German' :
                 formData.language}
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              onClick={onStartNew}
              variant="outline"
              className="h-12"
            >
              <Home className="mr-2 h-4 w-4" /> Create New Story
            </Button>
            <Button
              onClick={onViewLibrary}
              className="bg-amber hover:bg-amber-dark text-primary-foreground h-12"
            >
              <BookOpen className="mr-2 h-4 w-4" /> View My Library
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default StorySummaryStep;
