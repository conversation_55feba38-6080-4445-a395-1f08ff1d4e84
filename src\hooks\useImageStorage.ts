import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface ImageMetadata {
  id: string;
  story_id: string;
  scene_id: string;
  image_url: string;
  page_number: number;
  created_at: string;
}

export const useImageStorage = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const saveImageMetadata = async (
    storyId: string,
    sceneId: string,
    imageUrl: string,
    pageNumber: number
  ): Promise<ImageMetadata | null> => {
    try {
      setIsUploading(true);
      setError(null);

      const { data, error } = await supabase
        .from('story_images')
        .insert({
          story_id: storyId,
          scene_id: sceneId,
          image_url: imageUrl,
          page_number: pageNumber
        })
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save image metadata';
      setError(errorMessage);
      console.error('Image metadata save error:', err);
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const getStoryImages = async (storyId: string): Promise<ImageMetadata[]> => {
    try {
      setError(null);

      const { data, error } = await supabase
        .from('story_images')
        .select('*')
        .eq('story_id', storyId)
        .order('page_number', { ascending: true });

      if (error) {
        throw new Error(error.message);
      }

      return data || [];
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch story images';
      setError(errorMessage);
      console.error('Fetch story images error:', err);
      return [];
    }
  };

  const getAllImages = async (): Promise<ImageMetadata[]> => {
    try {
      setError(null);

      const { data, error } = await supabase
        .from('story_images')
        .select(`
          *,
          user_stories (
            title,
            main_character
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      return data || [];
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch all images';
      setError(errorMessage);
      console.error('Fetch all images error:', err);
      return [];
    }
  };

  const deleteImage = async (imageId: string): Promise<boolean> => {
    try {
      setError(null);

      const { error } = await supabase
        .from('story_images')
        .delete()
        .eq('id', imageId);

      if (error) {
        throw new Error(error.message);
      }

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete image';
      setError(errorMessage);
      console.error('Delete image error:', err);
      return false;
    }
  };

  return {
    saveImageMetadata,
    getStoryImages,
    getAllImages,
    deleteImage,
    isUploading,
    error
  };
};

export type { ImageMetadata };
