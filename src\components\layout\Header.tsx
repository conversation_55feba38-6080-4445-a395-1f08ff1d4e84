import React from 'react';
import { Button } from '@/components/ui/button';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { User, BookOpen, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HeaderProps {
  currentView: 'wizard' | 'library';
  onNavigate: (view: 'wizard' | 'library') => void;
}

const Header = ({ currentView, onNavigate }: HeaderProps) => {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        {/* Logo/Brand */}
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-amber to-orange rounded-lg flex items-center justify-center">
              <BookOpen className="h-5 w-5 text-white" />
            </div>
            <span className="font-bold text-xl text-slate-800">Story Genius</span>
          </div>
        </div>

        {/* Navigation Menu */}
        <NavigationMenu>
          <NavigationMenuList>
            {/* Home/Create Story */}
            <NavigationMenuItem>
              <Button
                variant={currentView === 'wizard' ? 'default' : 'ghost'}
                onClick={() => onNavigate('wizard')}
                className="flex items-center space-x-2"
              >
                <Home className="h-4 w-4" />
                <span>Create Story</span>
              </Button>
            </NavigationMenuItem>

            {/* Library */}
            <NavigationMenuItem>
              <Button
                variant={currentView === 'library' ? 'default' : 'ghost'}
                onClick={() => onNavigate('library')}
                className="flex items-center space-x-2"
              >
                <BookOpen className="h-4 w-4" />
                <span>Library</span>
              </Button>
            </NavigationMenuItem>

            {/* Account Menu */}
            <NavigationMenuItem>
              <NavigationMenuTrigger className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>Account</span>
              </NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid w-[200px] gap-3 p-4">
                  <li className="row-span-3">
                    <NavigationMenuLink asChild>
                      <div className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md">
                        <User className="h-6 w-6" />
                        <div className="mb-2 mt-4 text-lg font-medium">
                          Account
                        </div>
                        <p className="text-sm leading-tight text-muted-foreground">
                          Manage your account settings and preferences.
                        </p>
                      </div>
                    </NavigationMenuLink>
                  </li>
                  <ListItem title="Profile">
                    View and edit your profile information
                  </ListItem>
                  <ListItem title="Settings">
                    Manage your account settings
                  </ListItem>
                  <ListItem title="Sign Out">
                    Sign out of your account
                  </ListItem>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </div>
    </header>
  );
};

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & { title: string }
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  );
});
ListItem.displayName = "ListItem";

export default Header;
