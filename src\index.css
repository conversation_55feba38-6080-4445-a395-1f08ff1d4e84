
@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Original shadcn variables */
    --background: 40 100% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 37 100% 65%;
    --primary-foreground: 37 30% 25%;

    --secondary: 196 100% 70%;
    --secondary-foreground: 196 40% 20%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 280 60% 58%;
    --accent-foreground: 280 30% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;

    /* Sidebar variables */
    --sidebar-background: 40 100% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 196 100% 70%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 196 100% 70%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 37 100% 65%;
    --primary-foreground: 37 30% 25%;

    --secondary: 196 100% 70%;
    --secondary-foreground: 196 40% 20%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 280 60% 58%;
    --accent-foreground: 280 30% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  html, body {
    font-family: 'Quicksand', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Quicksand', sans-serif;
    font-weight: 700;
  }

  .font-dyslexic {
    font-family: 'Lexend', sans-serif;
    letter-spacing: 0.05em;
    line-height: 1.6;
  }

  .tap-target {
    min-height: 44px;
    min-width: 44px;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .story-container {
    @apply max-w-4xl mx-auto px-4 sm:px-6 py-8;
  }
  
  .emoji-hint {
    @apply inline-block animate-bounce-gentle mr-2;
  }

  .progress-step {
    @apply relative flex items-center justify-center w-10 h-10 rounded-full bg-muted text-muted-foreground font-medium;
  }

  .progress-step.active {
    @apply bg-primary text-primary-foreground;
  }

  .progress-step.completed {
    @apply bg-green-500 text-white;
  }

  .progress-connector {
    @apply flex-1 h-1 bg-muted;
  }

  .progress-connector.active {
    @apply bg-primary;
  }

  .style-chip {
    @apply rounded-xl border-2 border-transparent p-2 cursor-pointer transition-all;
  }

  .style-chip:hover {
    @apply border-secondary;
  }

  .style-chip.active {
    @apply border-primary bg-primary/10;
  }
}
